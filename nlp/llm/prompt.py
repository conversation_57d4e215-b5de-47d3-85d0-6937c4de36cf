import json
import re
from typing import Dict, Any, List

PROMPT_SHORT_NAME = {
    "influence_tagging": "ITG",
    "market_prediction": "PRD"
}


class PromptManager:
    """
    Manages different types of prompts for article analysis.
    Each prompt type has an associated parser to structure the results.
    """

    # System prompts for different analysis types
    SYSTEM_PROMPTS = {
        "influence_tagging": """You are a financial news analyst. Analyze the article and extract the following information:
        
Category: One of [Daily/Weekly Market Summary, Macro News, Company News, Sector News, Breaking News, Analyst & Opinion]
Regions: Up to 3 geographic focuses (e.g., US, Global, China, Europe, Japan)
Tags: Up to 5 comma-separated keywords (e.g., Nvidia, CPI, interest rates, recession, Treasury yields)
US Market Influence: Assess whether the article contains news likely to meaningfully influence the direction of the S&P 500 index. If yes, rate the potential impact on a scale from -5 (very bearish) to +5 (very bullish). Consider:
    Macroeconomic indicators (e.g., CPI, jobs report, GDP)
    Federal Reserve or central bank policy decisions
    Major corporate earnings or forward guidance
    Significant geopolitical or regulatory developments

Respond strictly in this format:

If the article does have meaningful market influence:
Category: <one from list>  
Regions: <up to 3 comma-separated items>  
Tags: <up to 5 comma-separated keywords>  
Influence: <integer from -5 to 5>  
Reason: <brief explanation within 100 words>

If the article does not have meaningful influence:
Category: <one from list>  
Regions: <up to 3 comma-separated items>  
Tags: <up to 5 comma-separated keywords>""",
        "market_prediction": """You are a senior S&P 500 market analyst with 20 years of experience. Your role is to analyze multiple news sources and predict market trends with specific evidence.

Your analysis must be:
- Direct and decisive (no hedging language)
- Evidence-based (cite specific facts from articles)
- Quantitative (provide specific percentage ranges)
- Actionable (clear predictions traders can use)

ANALYSIS STRUCTURE:

1. SHORT-TERM OUTLOOK (1-5 days)
Direction: [UP/DOWN/FLAT]
Expected Move: [specific % range]
Confidence: [HIGH/MEDIUM/LOW]
KEY EVIDENCE: List 2-3 most compelling facts from the articles that support this prediction

2. MEDIUM-TERM OUTLOOK (1-4 weeks)  
Direction: [UP/DOWN/FLAT]
Expected Move: [specific % range]
Confidence: [HIGH/MEDIUM/LOW]
KEY EVIDENCE: List 2-3 most compelling facts from the articles that support this prediction

3. LONG-TERM OUTLOOK (1-3 months)
Direction: [UP/DOWN/FLAT]
Expected Move: [specific % range]
Confidence: [HIGH/MEDIUM/LOW]
KEY EVIDENCE: List 2-3 most compelling facts from the articles that support this prediction

4. DOMINANT MARKET THEME
Identify the single most important theme across all articles that will drive the S&P 500.

5. CRITICAL LEVELS
Support: [specific level based on current market]
Resistance: [specific level based on current market]
Breakout/Breakdown trigger: [specific level and direction]

OUTPUT FORMAT: Always return valid JSON following this exact structure:
{
    "short_term": {
        "direction": "UP/DOWN/FLAT",
        "expected_move_pct": "X-Y%",
        "confidence": "HIGH/MEDIUM/LOW",
        "key_evidence": ["fact1", "fact2", "fact3"]
    },
    "medium_term": {
        "direction": "UP/DOWN/FLAT",
        "expected_move_pct": "X-Y%",
        "confidence": "HIGH/MEDIUM/LOW",
        "key_evidence": ["fact1", "fact2", "fact3"]
    },
    "long_term": {
        "direction": "UP/DOWN/FLAT",
        "expected_move_pct": "X-Y%",
        "confidence": "HIGH/MEDIUM/LOW",
        "key_evidence": ["fact1", "fact2", "fact3"]
    },
    "dominant_theme": "string",
    "critical_levels": {
        "support": number,
        "resistance": number,
        "trigger": "string"
    }
}"""
    }
    PROMPT_TEMPLATES = {
        "market_prediction": """CURRENT MARKET:
    Index: {index_level:.2f} {index_change_pct:.2f}% ({index_change_pct_week:.2f}% over the past week)
    VIX: {vix_level:.2f}
    Volume: {volume_vs_avg:.1f}x average
    Time: {time}

    NEWS TO ANALYZE:
    {articles_text}

    Analyze these {num_articles} articles and predict S&P 500 trends.""",
        "influence_tagging": """Title: {title} 
        Content: {content}"""
    }

    @classmethod
    def get_prompt(cls, prompt_type: str) -> Dict[str, str]:
        """
        Get the prompt template and system prompt for a specific analysis type.

        Args:
            prompt_type: Type of analysis to perform

        Returns:
            Dict containing prompt_template and system_prompt
        """
        return {
            "prompt_template": cls.PROMPT_TEMPLATES[prompt_type],
            "system_prompt": cls.SYSTEM_PROMPTS[prompt_type]
        }

    @classmethod
    def get_available_types(cls) -> List[str]:
        """Get list of available prompt types."""
        return list(cls.SYSTEM_PROMPTS.keys())

    @classmethod
    def parse_tagging(cls, completion: str) -> Dict[str, Any]:
        """Parse market analysis results into structured format."""
        result = {}

        # Extract category
        category_match = re.search(r"Category:\s*(.*?)(?:\n|$)", completion)
        if category_match:
            result["category"] = category_match.group(1).strip()

        # Extract regions
        region_match = re.search(r"Regions:\s*(.*?)(?:\n|$)", completion)
        if region_match:
            result["regions"] = [region.strip()
                                 for region in region_match.group(1).split(",")]

        # Extract tags
        tags_match = re.search(r"Tags:\s*(.*?)(?:\n|$)", completion)
        if tags_match:
            result["tags"] = [tag.strip()
                              for tag in tags_match.group(1).split(",")]

        return result

    @classmethod
    def parse_influence(cls, completion: str) -> Dict[str, Any]:
        result = {}
        # Extract influence
        influence_match = re.search(
            r"Influence:\s*(.*?)(?:\n|$)", completion)
        if influence_match:
            result["influence"] = int(influence_match.group(1).strip())

        # Extract reason
        reason_match = re.search(r"Reason:\s*(.*?)(?:\n|$)", completion)
        if reason_match:
            result["reason"] = reason_match.group(1).strip()

        return result

    @classmethod
    def parse_json(cls, completion: str) -> Dict[str, Any]:
        result = {}
        # Extract relevant
        try:
            result = json.loads(completion)
        except Exception as e:
            pass
        return result

    @classmethod
    def parse_influence_tagging(cls, completion: str) -> Dict[str, Any]:
        result = cls.parse_tagging(completion)
        result.update(cls.parse_influence(completion))
        return result

    @classmethod
    def parse_result(cls, completion: str, prompt_type: str) -> Dict[str, Any]:
        """
        Parse and format the completion based on the prompt type.

        Args:
            completion: Raw completion text from the model
            prompt_type: Type of analysis performed

        Returns:
            Dict containing parsed and formatted results
        """
        parsers = {
            "influence_tagging": cls.parse_influence_tagging,
            "market_prediction": cls.parse_json
        }

        if prompt_type not in parsers:
            return {"raw_completion": completion}

        try:
            return parsers[prompt_type](completion)
        except Exception as e:
            return {"raw_completion": completion, "parse_error": str(e)}
