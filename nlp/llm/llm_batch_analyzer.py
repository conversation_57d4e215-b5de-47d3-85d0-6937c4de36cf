import argparse
import async<PERSON>
from datetime import date, datetime, timedelta
import random
from typing import Any, Dict, List, Optional, Set
from contextlib import asynccontextmanager
from dataclasses import dataclass

from apis.llm.anthropic import AnthropicManager
from apis.llm.gemini import GeminiManager
from apis.llm.base import BaseAPIManager, BudgetLimitExeption, MaxTokensException, RateLimitExeption
from apis.llm.data_types import (
    ACTIVE_BATCH_STATUSES,
    COMPLETED_BATCH_STATUSES,
    INCOMPLETED_BATCH_STATUSES,
    BatchResponse,
    BatchStatus,
    CompletionRequest,
    CompletionStatus
)
from apis.llm.openai import OpenAIManager
from apis.yahoo_finance import yahoo_api

from db.database import DatabaseManager
from nlp.llm.prompt import PromptManager
from nlp.llm.helpers import (
    TZ,
    LLMConfig,
    extract_article_id,
    generate_custom_id,
    process_article
)

# Configure logging
from utils.logging_config import get_nlp_logger
logger = get_nlp_logger(
    __name__, log_file=f'llm/llm_batch_analyzer-{datetime.now().strftime("%Y-%m-%d-%H-%M-%S")}.log')

API_MAPPING = {
    'openai': OpenAIManager,
    'anthropic': AnthropicManager,
    'gemini': GeminiManager
}


@dataclass
class APIConfig:
    """Configuration for a specific API processor."""
    # Budget and rate limiting
    total_budget: float = 0.0
    requests_per_minute: int = 30
    max_enqueued_tokens: Optional[int] = None

    # Backoff configuration
    initial_backoff: float = 5.0  # Initial delay in seconds
    max_backoff: float = 300.0  # Maximum delay in seconds
    backoff_multiplier: float = 2.0  # Exponential backoff multiplier

    # Priority
    priority_level: int = 1  # Higher number = higher priority

    # LLM-specific configuration
    llm_config: Optional[LLMConfig] = None


@dataclass
class AsyncBatchConfig:
    """Configuration for async batch processing."""
    batch_size: int = 20  # Articles per batch
    queue_timeout: float = 2.0  # Timeout for queue operations in seconds
    batch_creation_interval: int = 5  # How often to create new batches
    batch_monitoring_interval: int = 60  # How often to check batch status
    batch_submission_interval: int = 10  # How often to submit batches to API
    max_retries: int = 3  # Max retries for failed batches
    initial_delay: float = 5.0  # Initial retry delay in seconds
    delay_multiplier: float = 2.0  # Exponential delay multiplier
    max_delay: float = 1800.0  # Maximum retry delay (30 minutes)
    shutdown_timeout: int = 3600  # Timeout for graceful shutdown in seconds


@dataclass
class DataSourceConfig:
    """Configuration for data source filtering."""
    max_articles: Optional[int] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    filter_target_dates: bool = False
    target_date_threshold: float = 0.01
    target_date_interval: str = "1d"
    target_date_days_before: int = 3
    target_date_days_after: int = 3
    min_words: int = 200


def get_target_dates(
    start_date: datetime,
    end_date: datetime,
    threshold: float = 0.01,
    interval: str = "1d",
    days_before: int = 3,
    days_after: int = 3,
) -> Set[date]:
    """
    Fetch dates where SPY's absolute daily return exceeded the given threshold.

    Args:
        start_date: Start date for analysis.
        end_date: End date for analysis.
        threshold: Minimum absolute daily return (e.g., 0.01 for 1%).
        interval: Data interval, default is "1d".
        days_before: How many days before the target date to include.
        days_after: How many days after the target date to include.

    Returns:
        Set of target dates (datetime.date).
    """
    try:
        df = yahoo_api.get_price_data(
            ticker="SPY",
            start_date=start_date,
            end_date=end_date,
            interval=interval
        )
    except Exception as e:
        logger.error(f"Error fetching price data: {e}")
        return set()

    if df.empty or "Close" not in df.columns:
        logger.warning("No price data available for the specified period")
        return set()

    df["return"] = df["Close"].pct_change()
    mask = df["return"].abs() > threshold
    filtered = df[mask]

    result = set()
    for ts in filtered.index:
        base_date = ts.replace(tzinfo=TZ).date()
        for offset in range(-days_before, days_after + 1):
            result.add(base_date + timedelta(days=offset))

    return result


@dataclass
class APIFailureState:
    """Tracks failure state for an API."""
    failure_count: int = 0
    last_failure_time: Optional[datetime] = None
    last_failure_reason: Optional[str] = None
    current_backoff: float = 0.0
    next_retry_time: Optional[datetime] = None
    exited: bool = False


@dataclass
class APIStats:
    """Statistics for an API."""
    successful_batches: int = 0
    failed_batches: int = 0
    total_processing_time: float = 0.0
    articles_processed: int = 0
    average_batch_time: float = 0.0
    total_cost: float = 0.0
    average_cost_per_article: float = 0.0
    last_success_time: Optional[datetime] = None
    last_failure_time: Optional[datetime] = None


@dataclass
class BatchItem:
    """Represents a batch item in the processing queue."""
    id: str
    articles: List[Dict[str, Any]]
    retry_count: int = 0
    last_error: Optional[str] = None
    next_retry_at: Optional[datetime] = None


class LLMBatchAnalyzer:
    """
    Async batch processor with queue-based architecture.
    """

    def __init__(self, api_configs: Dict[str, APIConfig],
                 prompt_type: str,
                 async_config: Optional[AsyncBatchConfig] = None,
                 data_source_config: Optional[DataSourceConfig] = None):
        self.db = DatabaseManager()
        self.prompt_manager = PromptManager()

        self.async_config = async_config or AsyncBatchConfig()
        # Initialize per-API configurations
        self.api_configs = api_configs
        self.data_source_config = data_source_config or DataSourceConfig()

        self.prompt_type = prompt_type
        self.llm_apis = self._initialize_llm_apis()

        # Multi-queue priority system
        self.queues = {
            api_name: asyncio.Queue() for api_name in self.llm_apis.keys()
        }
        self.queues['retry'] = asyncio.Queue()

        # Async coordination
        self.running = True
        self.shutdown_event = asyncio.Event()
        self.graceful_shutdown_initiated_event = asyncio.Event()

        # Per-API state tracking
        self.api_failures: Dict[str, APIFailureState] = {
            api: APIFailureState() for api in self.llm_apis.keys()}
        self.api_stats: Dict[str, APIStats] = {
            api: APIStats() for api in self.llm_apis.keys()}

        # Global request tracking
        self.active_requests: Dict[str, BatchItem] = {}
        self.global_stats = {
            'batches_created': 0,
            'batches_completed': 0,
            'batches_failed': 0,
            'batches_retried': 0,
            'articles_processed': 0,
            'total_cost': 0,
            'average_cost_per_article': 0.0
        }

    def _initialize_llm_apis(self) -> Dict[str, BaseAPIManager]:
        """Initialize the LLM APIs."""

        llm_apis = {}
        for api_name, api_config in self.api_configs.items():
            current_cost = self.db.llm_api_service.get_total_cost(
                api=api_name, prompt_type=self.prompt_type)

            current_enqueued_tokens = self.db.llm_api_service.get_total_input_tokens(
                api=api_name, included_status=ACTIVE_BATCH_STATUSES + [BatchStatus.FAILED.value], time_window_hours=24
            )
            llm_apis[api_name] = API_MAPPING[api_name](
                total_budget=api_config.total_budget,
                total_cost=current_cost,
                requests_per_minute=api_config.requests_per_minute,
                max_enqueued_tokens=api_config.max_enqueued_tokens,
                enqueued_tokens=current_enqueued_tokens
            )
            logger.info(f"Initialized {api_name} API with: "
                        f"budget {api_config.total_budget}, "
                        f"current cost {current_cost}, "
                        f"current enqueued tokens {current_enqueued_tokens}")
        return llm_apis

    def is_api_available(self, api_name: str) -> bool:
        """Check if an API is available based on failure state, backoff timers, and concurrent batch limits."""
        if api_name not in self.api_configs:
            return False

        failure_state = self.api_failures[api_name]
        if failure_state.exited:
            return False

        # Check if we're in backoff period
        if failure_state.next_retry_time and datetime.now() < failure_state.next_retry_time:
            return False

        return True

    async def handle_api_failure(self, api_name: str, error: Exception, batch_item: BatchItem) -> None:
        """Manage failure tracking, backoff calculation, and retry queue placement."""
        current_time = datetime.now()

        # Update api failure state
        failure_state = self.api_failures[api_name]
        failure_state.failure_count += 1
        failure_state.last_failure_time = current_time
        failure_state.last_failure_reason = error

        # Calculate backoff delay and set next retry time
        api_config = self.api_configs[api_name]
        new_backoff = api_config.initial_backoff if failure_state.current_backoff == 0 else min(
            failure_state.current_backoff * api_config.backoff_multiplier,
            api_config.max_backoff
        )
        failure_state.current_backoff = new_backoff
        failure_state.next_retry_time = current_time + \
            timedelta(seconds=new_backoff)

        logger.warning(f"API {api_name} failure state: {failure_state}")

        # Update API stats
        api_stats = self.api_stats[api_name]
        api_stats.failed_batches += 1
        api_stats.last_failure_time = current_time

        # Check if we should retry this batch
        if batch_item.retry_count < self.async_config.max_retries:
            # Calculate retry delay
            retry_delay = min(
                self.async_config.initial_delay *
                (self.async_config.delay_multiplier ** batch_item.retry_count),
                self.async_config.max_delay
            )

            batch_item.retry_count += 1
            batch_item.next_retry_at = current_time + \
                timedelta(seconds=retry_delay)
            batch_item.last_error = error

            # Send to retry queue
            await asyncio.wait_for(
                self.queues['retry'].put(batch_item),
                timeout=self.async_config.queue_timeout
            )
            logger.warning(f"API {api_name} failed, sending batch {batch_item.id} to retry queue "
                           f"(attempt {batch_item.retry_count}/{self.async_config.max_retries}, retry in {retry_delay:.1f}s)")
        else:
            # Exceeded max retries, mark as permanently failed
            batch_item.last_error = error
            logger.error(
                f"Batch {batch_item.id} failed after maximum retries")

        logger.warning(
            f"API {api_name} failed (#{failure_state.failure_count}), backing off for {new_backoff:.1f}s")

    async def route_batch_by_priority(self, batch_item: BatchItem):
        """Determine which queue to use based on batch characteristics and API availability."""
        available_apis = []
        priorities = []
        for api_name in self.llm_apis.keys():
            if self.is_api_available(api_name):
                api_config = self.api_configs[api_name]
                available_apis.append(api_name)
                priorities.append(api_config.priority_level)

        total_priority = sum(priorities)
        if total_priority == 0:
            raise ValueError("Total priority cannot be zero")

        normalized = [p / total_priority for p in priorities]
        selected = random.choices(available_apis, weights=normalized, k=1)[0]

        await asyncio.wait_for(
            self.queues[selected].put(batch_item),
            timeout=self.async_config.queue_timeout
        )
        logger.info(f"Queued batch {batch_item.id} in {selected} queue. ")

    async def release_queue(self, api_name: str):
        """Relase queue and transfer all batches to other queues."""
        logger.info(
            f"Transfering {self.queues[api_name].qsize()} items from {api_name} queue")
        while not self.queues[api_name].empty():
            batch_item = await asyncio.wait_for(
                self.queues[api_name].get(),
                timeout=self.async_config.queue_timeout
            )
            await self.route_batch_by_priority(batch_item)
            self.queues[api_name].task_done()
        logger.info(f"Released {api_name} queue")

    async def initiate_graceful_shutdown(self):
        """Initiate graceful shutdown sequence."""
        if self.graceful_shutdown_initiated_event.is_set():
            return  # Shutdown already in progress

        logger.info("Initiating graceful shutdown sequence")

        # Wait a brief moment for any final batches to be created
        await asyncio.sleep(5)

        self.graceful_shutdown_initiated_event.set()

        # Check if queues are drained and no active requests
        await self._wait_for_queue_drain()

        # Set shutdown event
        self.stop_processing()

    async def _wait_for_queue_drain(self):
        """Wait for all queues to be drained and active requests to complete."""
        logger.info(
            "Waiting for queues to drain and active requests to complete")

        max_wait_time = self.async_config.shutdown_timeout  # Maximum time to wait
        check_interval = 30  # Check every 10 seconds
        start_time = datetime.now()

        while (datetime.now() - start_time).total_seconds() < max_wait_time:
            queues_empty = all([queue.empty()
                               for queue in self.queues.values()])

            no_active_requests = len(self.active_requests) == 0

            if queues_empty and no_active_requests:
                logger.info("All queues drained and no active requests")
                break

            logger.info(f"Waiting queues: {', '.join([f'{k}={v.qsize()}' for k, v in self.queues.items()])}, "
                        f"active_requests={len(self.active_requests)}")

            await asyncio.sleep(check_interval)
        else:
            logger.warning(
                f"Timeout waiting for queues to drain after {max_wait_time}s")

    def get_article_candidates(self, limit: int = 1000) -> List[Dict[str, Any]]:
        """Get articles that need processing."""
        # Get date range
        config = self.data_source_config
        start_dt, end_dt = self.db.article_service.get_article_date_range()
        if config.start_date:
            start_dt = max(start_dt, datetime.strptime(
                config.start_date, "%Y-%m-%d").replace(tzinfo=TZ))
        if config.end_date:
            end_dt = min(end_dt, datetime.strptime(
                config.end_date, "%Y-%m-%d").replace(tzinfo=TZ))

        # Get target dates
        target_dates = get_target_dates(
            start_dt, end_dt, config.target_date_threshold,
            config.target_date_interval, config.target_date_days_before,
            config.target_date_days_after
        ) if config.filter_target_dates else None

        if target_dates:
            logger.info(f"Filter articles on {len(target_dates)} target dates")

        articles = self.db.article_service.get_articles_for_llm_batch(
            # Exclude already succeeded articles with the api and prompt type
            prompt_type=self.prompt_type,
            excluded_statuses=[
                CompletionStatus.SUCCEEDED.value,
                CompletionStatus.IN_PROGRESS.value
            ],
            date_list=target_dates,
            start_date=start_dt,
            end_date=end_dt,
            limit=limit,
            # Filter short articles
            min_words=config.min_words
        )
        if start_dt and end_dt:
            logger.info(
                f"Fetched {len(articles)} articles between {start_dt.strftime('%Y-%m-%d')} and {end_dt.strftime('%Y-%m-%d')}")
        else:
            logger.info(f"Fetched {len(articles)} articles")

        return articles

    def save_completion(self, result_dict: Dict[str, Any], api_name: str, batch_id: str) -> Dict[str, Any]:
        """Save completion result to database."""
        try:
            article_id = extract_article_id(result_dict['custom_id'])
            db_record = {
                "id": result_dict['custom_id'],
                'article_id': article_id,
                "api": api_name,
                "prompt_type": self.prompt_type,
                "model": result_dict['model'],
                "status": result_dict['status'],
                "batch_id": batch_id,
                "raw_response": result_dict.get('raw_response'),
                "input_tokens": result_dict.get('input_tokens') or result_dict.get('estimated_tokens'),
                "output_tokens": result_dict.get('output_tokens')
            }
            # if 'user_prompt' in result_dict and 'system_prompt' in result_dict:
            #     db_record['prompt'] = result_dict['system_prompt'] + \
            #         result_dict['user_prompt']

            if 'cost' in result_dict:
                db_record['cost'] = result_dict['cost']

            if 'content' in result_dict:
                parsed_result = self.prompt_manager.parse_result(
                    result_dict['content'], self.prompt_type)
                db_record['content'] = parsed_result

                # Save to article metadata
                if self.db.article_service.add_article_metadata(
                        article_id, {self.prompt_type: db_record['content']}):
                    logger.debug(
                        f"Successfully saved in article metadata: {article_id} ")
                else:
                    logger.error(
                        f"Failed to save in article metadata: {article_id}")

            if self.db.llm_api_service.upsert_llm_result(db_record):
                logger.debug(
                    f"Successfully saved result: {result_dict['custom_id']}")
            else:
                logger.error(
                    f"Failed to save result: {result_dict['custom_id']}")

            return db_record
        except Exception as e:
            logger.error(f"Error saving result: {e}")

        return {}

    def save_batch(self, batch_dict: Dict[str, Any], api_name: str) -> Dict[str, Any]:
        """Save batch request information to database."""
        try:
            db_record = {
                'id': batch_dict['id'],
                'api': api_name,
                'status': batch_dict['status'],
                'created_at': batch_dict['created_at'],
                'completed_at': batch_dict.get('completed_at'),
                'expires_at': batch_dict['expires_at'],
                'prompt_type': self.prompt_type,
                'raw_response': batch_dict['raw_response'],
                'cost': batch_dict.get('cost'),
                'input_tokens': batch_dict.get('input_tokens') or batch_dict.get('estimated_tokens'),
                'output_tokens': batch_dict.get('output_tokens')
            }
            db_result = self.db.llm_api_service.upsert_llm_batch(db_record)

            if db_result:
                logger.debug(f"Successfully saved batch: {batch_dict['id']}")
                return db_record
            else:
                logger.error(f"Failed to save batch: {batch_dict['id']}")
        except Exception as e:
            logger.error(f"Error saving batch: {batch_dict['id']}. Error: {e}")

        return {}

    def stop_processing(self):
        """Stop all processing gracefully."""
        self.running = False
        self.shutdown_event.set()
        logger.info("Batch processing stop requested")

    # ============================================================================
    # COMPONENT 1: BATCH CREATOR
    # ============================================================================

    async def batch_creator_component(self) -> None:
        """
        Component 1: Batch Creator

        Continuously reads articles from database, groups them into batches,
        and puts them into the shared queue for processing.
        """
        logger.info(f"Starting Batch Creator component for {self.prompt_type}")
        batch_size = self.async_config.batch_size
        max_articles = self.data_source_config.max_articles

        try:
            while self.running and not self.shutdown_event.is_set():
                try:
                    # Get articles that need processing
                    articles = self.get_article_candidates(limit=max_articles)

                    if articles:
                        logger.info(
                            f"Batch Creator: Found {len(articles)} articles to process")

                        # Split articles into batches using dynamic batch sizing
                        for i in range(0, len(articles), batch_size):
                            if not self.running or self.shutdown_event.is_set() or self.graceful_shutdown_initiated_event.is_set():
                                break

                            article_batch = articles[i:i + batch_size]
                            batch_item_id = f"batch_{self.prompt_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i//batch_size}"

                            batch_item = BatchItem(
                                id=batch_item_id,
                                articles=article_batch
                            )
                            # Put batch in appropriate queue (with timeout to avoid blocking)
                            logger.info(f"Batch Creator: Queuing batch {batch_item.id} "
                                        f"with {len(batch_item.articles)} articles")
                            await self.route_batch_by_priority(batch_item)
                            self.global_stats['batches_created'] += 1

                            # Wait for next polling cycle - shorter interval during graceful shutdown
                            sleep_interval = self.async_config.batch_creation_interval
                            logger.info(
                                f"Batch Creator: Sleeping for {sleep_interval}s...")
                            await asyncio.sleep(sleep_interval)
                    else:
                        logger.info(
                            f"Batch Creator: No articles found for {self.prompt_type}. Initiating graceful shutdown...")
                except Exception as e:
                    logger.error(
                        f"Batch Creator: Error in processing cycle: {e}")
                await self.initiate_graceful_shutdown()
                break

        except asyncio.CancelledError:
            logger.info("Batch Creator: Component cancelled")
            raise
        except Exception as e:
            logger.error(f"Batch Creator: Fatal error: {e}")
            raise
        finally:
            logger.info("Batch Creator: Component stopped")

    # ============================================================================
    # BATCH PROCESSOR COMPONENT
    # ============================================================================

    async def _create_batch_request(self, api_name: str, batch_item: BatchItem) -> BatchResponse | None:
        """Create a batch request for the specified API with enhanced error handling."""
        llm_api = self.llm_apis[api_name]
        llm_config = self.api_configs[api_name].llm_config

        try:
            requests = []
            prompt = self.prompt_manager.get_prompt(self.prompt_type)
            system_prompt = prompt['system_prompt']
            model = llm_config.model

            for article in batch_item.articles:
                article_input = process_article(
                    article, llm_config.min_input, llm_config.max_input, logger=logger)
                if not article_input:
                    continue

                cid = generate_custom_id(
                    article['id'], self.prompt_type, api_name)

                formatted_prompt = prompt['prompt_template'].format(
                    title=article_input['title'],
                    content=article_input['content']
                )

                request = CompletionRequest(
                    max_tokens=llm_config.max_tokens,
                    temperature=llm_config.temperature,
                    user_prompt=formatted_prompt,
                    system_prompt=system_prompt,
                    model=model,
                    custom_id=cid
                )
                requests.append(request)

            if not requests:
                logger.warning(
                    f"{api_name} Processor: No valid requests in batch {batch_item.id}")
                return None

            batch = llm_api.get_completion_batch(requests)

            if not batch:
                logger.error(
                    f"{api_name} Processor: Failed to create batch for {batch_item.id}")
                return None

            if not batch or batch.status == BatchStatus.FAILED.value:
                logger.error(
                    f"{api_name} Processor: Batch {batch.id} failed, status {batch.status}")
                return None

            logger.info(
                f"{api_name} Processor: Created batch {batch.id}, status {batch.status}")

            # Save batch and requests to database
            db_result = self.save_batch(batch.to_dict(), api_name)
            if db_result:
                self.active_requests[batch.id] = batch_item

            for request in requests:
                request_dict = request.to_dict()
                request_dict['status'] = CompletionStatus.IN_PROGRESS.value
                self.save_completion(request_dict, api_name, batch_id=batch.id)

            return batch

        except BudgetLimitExeption as e:
            logger.error(f"{api_name} Processor: "
                         f"Budget limit reached for batch {batch_item.id}: {e}")
            raise
        except MaxTokensException as e:
            logger.error(f"{api_name} Processor: "
                         f"Max enqueued tokens exceeded for batch {batch_item.id}: {e}")
            return None
        except RateLimitExeption as e:
            logger.error(f"{api_name} Processor: "
                         f"Rate limit reached for batch {batch_item.id}: {e}")
            return None
        except Exception as e:
            logger.error(f"{api_name} Processor: "
                         f"Error creating batch {batch_item.id}: {e}")
            return None

    async def batch_processor_component(self, api_name: str) -> None:
        """
        Batch Processor Component

        Processes batches from multiple priority queues based on API configuration.
        Implements per-API exponential backoff, failure tracking, and load balancing.
        """
        api_config = self.api_configs[api_name]
        logger.info(
            f"Starting {api_name.title()} Processor component (Priority: {api_config.priority_level})")

        # Wait for batch creator to start
        await asyncio.sleep(2)

        try:
            while self.running and not self.shutdown_event.is_set():
                try:
                    # Check if API is available (not in backoff, under concurrent limit)
                    if not self.is_api_available(api_name):
                        next_retry_time = self.api_failures[api_name].next_retry_time
                        if next_retry_time:
                            delay = (next_retry_time -
                                     datetime.now()).total_seconds()
                            logger.info(
                                f"{api_name} Processor: Waiting for next retry time ({delay:.1f}s)")
                            await asyncio.sleep(delay)
                        continue

                    # Try to get batch from queues based on API permissions and priority
                    batch_item = None
                    queue_source = None

                    # Priority 1: Retry queue
                    if not self.queues['retry'].empty():
                        try:
                            batch_item = await asyncio.wait_for(
                                self.queues['retry'].get(),
                                timeout=self.async_config.queue_timeout
                            )
                            queue_source = "retry"
                        except asyncio.TimeoutError:
                            pass

                    # Priority 2: API-specific queue
                    if not batch_item and not self.queues[api_name].empty():
                        try:
                            batch_item = await asyncio.wait_for(
                                self.queues[api_name].get(),
                                timeout=self.async_config.queue_timeout
                            )
                            queue_source = api_name
                        except asyncio.TimeoutError:
                            pass

                    if batch_item:
                        logger.info(f"{api_name} Processor: Processing batch {batch_item.id} "
                                    f"from {queue_source} queue with {len(batch_item.articles)} articles")

                        # Attempt to create batch
                        batch = await self._create_batch_request(api_name, batch_item)
                        if not batch:
                            logger.warning(
                                f"{api_name} Processor: Failed to create batch {batch_item.id}")
                            await self.handle_api_failure(api_name, Exception("Failed to create batch"), batch_item)

                        # Mark queue task as done
                        if queue_source == "retry":
                            self.queues['retry'].task_done()
                            self.global_stats['batches_retried'] += 1
                        else:
                            self.queues[api_name].task_done()
                    else:
                        logger.info(
                            f"{api_name} Processor: No batches available from all queues")
                        if self.graceful_shutdown_initiated_event.is_set():
                            all_queues_empty = all([
                                queue.empty() for queue in self.queues.values()
                            ])
                            if all_queues_empty:
                                logger.info(
                                    f"{api_name} Processor: Graceful shutdown initiated, exiting")
                                break

                except BudgetLimitExeption as e:
                    logger.error(f"{api_name} Processor: "
                                 f"Budget limit reached: {e}")
                    raise
                except Exception as e:
                    logger.error(
                        f"{api_name} Processor: Error processing batch: {e}")

                # Wait for next polling cycle - shorter interval during graceful shutdown
                sleep_interval = self.async_config.batch_submission_interval
                logger.info(
                    f"{api_name} Batch Processor: Sleeping for {sleep_interval}s...")
                await asyncio.sleep(sleep_interval)

        except asyncio.CancelledError:
            logger.info(f"{api_name} Processor: Component cancelled")
            raise
        except Exception as e:
            logger.error(f"{api_name} Processor: Fatal error: {e}")
            raise
        finally:
            self.api_failures[api_name].exited = True
            await self.release_queue(api_name)
            logger.info(f"{api_name} Processor: Component stopped")

    # ============================================================================
    # COMPONENT 4: BATCH MONITOR
    # ============================================================================

    async def _process_single_batch(self, batch_record: Dict[str, Any]) -> None:
        """Process a single batch and handle results."""
        try:
            api_name = batch_record['api']
            batch_id = batch_record['id']
            llm_api = self.llm_apis.get(api_name)
            api_stats = self.api_stats[api_name]

            if not llm_api:
                logger.error(f"Batch Monitor: Unknown API: {api_name}")
                return

            # Retrieve batch status and results
            batch = llm_api.retrieve_batch(batch_id)
            if not batch:
                logger.error(
                    f"Batch Monitor: Failed to retrieve batch {batch_id}")
                return

            logger.debug(
                f"Batch Monitor: Retrieved batch {batch.id} with status {batch.status}")

            if batch.status in COMPLETED_BATCH_STATUSES:
                logger.info(
                    f"Batch Monitor: Batch {batch.id} completed. Processing {len(batch.completion_results)} results...")

                # Process completed results
                processed_results = 0
                for result in batch.completion_results:
                    if self.save_completion(result.to_dict(), api_name, batch_id=batch.id):
                        processed_results += 1
                logger.info(
                    f"Batch Monitor: Processed {processed_results} results for batch {batch.id}")

                # Mark batch as processed
                batch.status = BatchStatus.PROCESSED.value

                # Update global stats
                self.global_stats['batches_completed'] += 1
                self.global_stats['articles_processed'] += processed_results
                self.global_stats['total_cost'] += batch.cost
                self.global_stats['average_cost_per_article'] = self.global_stats['total_cost'] / \
                    self.global_stats['articles_processed']

                # Update API stats
                api_stats.successful_batches += 1
                api_stats.articles_processed += processed_results
                api_stats.total_cost += batch.cost
                api_stats.average_cost_per_article = api_stats.total_cost / \
                    api_stats.articles_processed
                api_stats.last_success_time = datetime.now()
                api_stats.total_processing_time += (
                    batch.completed_at - batch.created_at).total_seconds()
                api_stats.average_batch_time = api_stats.total_processing_time / \
                    api_stats.successful_batches

                # Reset backoff
                self.api_failures[api_name].current_backoff = 0
                self.api_failures[api_name].next_retry_time = None

                # Remove from active tracking
                self.active_requests.pop(batch_id, None)

            elif batch.status in INCOMPLETED_BATCH_STATUSES:
                logger.error(
                    f"Batch Monitor: Batch {batch.id} failed")

                # Mark all database records as failed
                records = self.db.llm_api_service.get_llm_results(
                    batch_id=batch.id)
                logger.info(
                    f"Batch Monitor: Marking {len(records)} database records as failed...")
                for record in records:
                    record['status'] = CompletionStatus.FAILED.value
                    self.db.llm_api_service.upsert_llm_result(record)

                # Update global stats
                self.global_stats['batches_failed'] += 1

                # Remove from active tracking
                batch_item = self.active_requests.pop(batch_id, None)
                if batch_item:
                    await self.handle_api_failure(api_name, Exception("Batch failed"), batch_item)

            elif batch.status in ACTIVE_BATCH_STATUSES:
                logger.debug(
                    f"Batch Monitor: Batch {batch.id} is still active")
            else:
                logger.warning(
                    f"Batch Monitor: Unknown batch status {batch.status} for batch {batch.id}")

            # Update batch status in database
            if self.save_batch(batch.to_dict(), api_name):
                logger.debug(
                    f"Batch Monitor: Successfully updated batch status for {batch.id}")
            else:
                logger.error(
                    f"Batch Monitor: Failed to update batch status for {batch.id}")

        except Exception as e:
            logger.error(
                f"Batch Monitor: Error processing batch {batch_record.get('id', 'unknown')}: {e}")

    async def batch_monitor_component(self) -> None:
        """
        Component 4: Batch Monitor

        Continuously polls all active batch requests across both APIs,
        checks batch status, and processes completed results.
        """
        logger.info(f"Starting Batch Monitor component for {self.prompt_type}")

        try:
            while self.running and not self.shutdown_event.is_set():
                try:
                    # Get active batches from database
                    active_batches = self.db.llm_api_service.get_llm_batch_status(
                        apis=list(self.llm_apis.keys()),
                        prompt_type=self.prompt_type,
                        included_status=ACTIVE_BATCH_STATUSES + COMPLETED_BATCH_STATUSES
                    )

                    if active_batches:
                        logger.info(
                            f"Batch Monitor: Found {len(active_batches)} active batches to check")

                        for batch_id in [batch['id'] for batch in active_batches]:
                            if batch_id not in self.active_requests:
                                logger.debug(
                                    f"Batch Monitor: Batch {batch_id} is active but not tracked")
                                self.active_requests[batch_id] = None

                        # Process batches concurrently but with limited concurrency
                        # Limit concurrent batch processing
                        semaphore = asyncio.Semaphore(5)

                        async def process_with_semaphore(batch_record):
                            async with semaphore:
                                await self._process_single_batch(batch_record)
                                # Small delay between batch processing
                                await asyncio.sleep(0.5)

                        # Process all active batches concurrently
                        await asyncio.gather(
                            *[process_with_semaphore(batch_record)
                                for batch_record in active_batches],
                            return_exceptions=True
                        )
                    else:
                        logger.info(
                            f"Batch Monitor: No active batches found for {self.prompt_type}")
                except Exception as e:
                    logger.error(
                        f"Batch Monitor: Error in monitoring cycle: {e}")

                # Wait for next polling cycle - shorter interval during graceful shutdown
                sleep_interval = self.async_config.batch_monitoring_interval
                logger.info(
                    f"Batch Monitor: Sleeping for {sleep_interval}s...")
                await asyncio.sleep(sleep_interval)

        except asyncio.CancelledError:
            logger.info("Batch Monitor: Component cancelled")
            raise
        except Exception as e:
            logger.error(f"Batch Monitor: Fatal error: {e}")
            raise
        finally:
            logger.info("Batch Monitor: Component stopped")

    # ============================================================================
    # MAIN ORCHESTRATION METHODS
    # ============================================================================

    async def run_async_batch_processing(self) -> None:
        """
        Main method to run all four async components concurrently.

        This orchestrates the entire async batch processing pipeline:
        1. Batch Creator - reads articles and creates batches
        2. OpenAI Processor - processes batches with high priority
        3. Anthropic Processor - processes remaining batches
        3. Gemini Processor - processes remaining batches
        4. Batch Monitor - monitors and processes completed batches
        """
        logger.info(
            f"Starting async batch processing pipeline for {self.prompt_type}")
        logger.info(f"Configuration: {self.async_config}")

        try:
            # Create all async tasks including graceful shutdown coordinator
            tasks = [
                asyncio.create_task(
                    self.batch_creator_component(),
                    name="BatchCreator"
                ),
                asyncio.create_task(
                    self.batch_monitor_component(),
                    name="BatchMonitor"
                )
            ]
            for api_name in self.llm_apis.keys():
                tasks.append(
                    asyncio.create_task(
                        self.batch_processor_component(api_name),
                        name=f"{api_name.title()}Processor"
                    )
                )

            logger.info("All components started, running concurrently...")

            # Run all components concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Check for exceptions in any component
            for i, result in enumerate(results):
                task_name = tasks[i].get_name()
                if isinstance(result, Exception):
                    logger.error(f"Exception in {task_name}: {result}")
                else:
                    logger.info(f"{task_name} completed successfully")

        except asyncio.CancelledError:
            logger.info("Async batch processing cancelled")
            raise
        except Exception as e:
            logger.error(f"Fatal error in async batch processing: {e}")
            raise
        finally:
            logger.info("Async batch processing pipeline stopped")

    @asynccontextmanager
    async def async_context(self):
        """Async context manager for proper resource cleanup."""
        try:
            logger.info("LLMBatchAnalyzer: Async context started")
            yield self
        finally:
            logger.info("LLMBatchAnalyzer: Resources cleaned up")

            # Print final statistics
            logger.info("=== Final Processing Statistics ===")

            logger.info("Global Statistics:")
            for key, value in self.global_stats.items():
                logger.info(f"  {key}: {value}")

            logger.info("Per-API Statistics:")
            for api_name, stats in self.api_stats.items():
                logger.info(f"  {api_name}:")
                logger.info(
                    f"    Successful batches: {stats.successful_batches}")
                logger.info(f"    Failed batches: {stats.failed_batches}")
                logger.info(
                    f"    Articles processed: {stats.articles_processed}")
                logger.info(
                    f"    Average batch time: {stats.average_batch_time:.2f}s")
                logger.info(
                    f"    Average cost per article: ${stats.average_cost_per_article:.6f}")
                logger.info(f"    Total cost: ${stats.total_cost:.4f}")

            logger.info("Per-API Failure States:")
            for api_name, failure_state in self.api_failures.items():
                logger.info(f"  {api_name}:")
                logger.info(
                    f"    Total failures: {failure_state.failure_count}")
                logger.info(
                    f"    Last failure time: {failure_state.last_failure_time}")
                logger.info(
                    f"    Last failure reason: {failure_state.last_failure_reason}")
                logger.info(
                    f"    Current backoff: {failure_state.current_backoff:.1f}s")

            logger.info("Async batch processing pipeline stopped")


# ============================================================================
# COMMAND LINE INTERFACE
# ============================================================================

async def main():
    """Main function to run the analyzer from command line."""
    parser = argparse.ArgumentParser(
        description='LLM Batch Analyzer - Advanced async batch processing')
    parser.add_argument('-t', '--prompt-type',
                        default='influence_tagging', help='Type of analysis to perform')
    parser.add_argument('-a', '--enabled-apis',
                        default='openai,anthropic,gemini', help='Comma-separated list of enabled APIs')
    parser.add_argument('-s', '--start-date', help='Start date (YYYY-MM-DD)')
    parser.add_argument('-e', '--end-date', help='End date (YYYY-MM-DD)')
    parser.add_argument('--filter-target-dates', action='store_true',
                        help='Filter dates based on SPY price movements')
    parser.add_argument('--max-articles', type=int, default=1000,
                        help='Maximum articles to process per cycle')
    parser.add_argument('--openai-budget', type=float, default=0.1,
                        help='Maximum budget for OpenAI API calls')
    parser.add_argument('--anthropic-budget', type=float, default=0.1,
                        help='Maximum budget for Anthropic API calls')
    parser.add_argument('--gemini-budget', type=float, default=0.1,
                        help='Maximum budget for Gemini API calls')
    parser.add_argument('--requests-per-minute', type=int, default=50,
                        help='Rate limit per minute')
    parser.add_argument('--batch-size', type=int, default=30,
                        help='Batch size for processing')

    # LLM configs
    parser.add_argument('--max-tokens', type=int, default=150,
                        help='Maximum tokens for LLM output')
    parser.add_argument('--temperature', type=float, default=0.1,
                        help='Temperature for LLM output')

    args = parser.parse_args()

    try:
        # Create per-API configurations from command line args
        api_configs = {
            'openai': APIConfig(
                total_budget=args.openai_budget,
                requests_per_minute=args.requests_per_minute,
                priority_level=10,
                max_enqueued_tokens=2_000_000,  # 2M tokens
                llm_config=LLMConfig(
                    model='gpt-4.1-mini',
                    max_tokens=args.max_tokens,
                    temperature=args.temperature
                ),
            ),
            'anthropic': APIConfig(
                total_budget=args.anthropic_budget,
                requests_per_minute=args.requests_per_minute,
                priority_level=5,
                llm_config=LLMConfig(
                    model='claude-3-5-haiku-20241022',
                    max_tokens=args.max_tokens,
                    temperature=args.temperature
                ),
            ),
            'gemini': APIConfig(
                total_budget=args.gemini_budget,
                requests_per_minute=args.requests_per_minute,
                priority_level=50,
                llm_config=LLMConfig(
                    model='gemini-2.0-flash-001',
                    max_tokens=args.max_tokens,
                    temperature=args.temperature
                ),
            )
        }
        async_config = AsyncBatchConfig(
            batch_size=args.batch_size
        )
        data_source_config = DataSourceConfig(
            max_articles=args.max_articles,
            start_date=args.start_date,
            end_date=args.end_date,
            filter_target_dates=args.filter_target_dates
        )

        api_configs = {api: config for api, config in api_configs.items()
                       if api in args.enabled_apis.split(',')}

        # Initialize analyzer
        batch_analyzer = LLMBatchAnalyzer(
            api_configs, args.prompt_type, async_config, data_source_config)

        # Run the async batch processing pipeline
        async with batch_analyzer.async_context():
            try:
                logger.info("=== LLM Batch Analyzer Starting ===")
                logger.info(f"Prompt type: {args.prompt_type}")
                logger.info(f"OpenAI Budget: ${args.openai_budget}")
                logger.info(f"Anthropic Budget: ${args.anthropic_budget}")
                logger.info(f"Gemini Budget: ${args.gemini_budget}")
                logger.info(f"Async Config: {async_config}")
                logger.info("Starting async batch processing pipeline...")

                await batch_analyzer.run_async_batch_processing()
            except KeyboardInterrupt:
                logger.info(
                    "Received interrupt signal, stopping processing...")
                await batch_analyzer.initiate_graceful_shutdown()
            except Exception as e:
                logger.error(f"Unexpected error during batch processing: {e}")
                await batch_analyzer.initiate_graceful_shutdown()

    except KeyboardInterrupt:
        logger.info("Received interrupt signal, handled.")
    except Exception as e:
        logger.error(f"Application error: {e}")
        raise


if __name__ == '__main__':
    asyncio.run(main())
