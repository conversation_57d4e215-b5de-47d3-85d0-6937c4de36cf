"""
Response parser for LLM-based market prediction.

This module parses LLM responses and extracts structured prediction data
including market direction, confidence scores, and reasoning.
"""

import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

from utils.logging_config import get_predictor_logger

# Configure logger
logger = get_predictor_logger(__name__)


class LLMResponseParser:
    """Parses LLM responses into structured market predictions."""
    
    def __init__(self):
        """Initialize the response parser."""
        pass
    
    def parse_market_prediction(
        self, 
        response_content: str,
        api_name: str = "unknown",
        model_name: str = "unknown"
    ) -> Dict[str, Any]:
        """
        Parse LLM response into structured market prediction.
        
        Args:
            response_content: Raw response content from LLM
            api_name: Name of the API used
            model_name: Name of the model used
            
        Returns:
            Structured prediction dictionary
        """
        try:
            # First try to parse as JSON (expected format)
            prediction_data = self._parse_json_response(response_content)
            
            if prediction_data:
                return self._format_prediction_result(
                    prediction_data, api_name, model_name, response_content
                )
            
            # Fallback to text parsing if JSON parsing fails
            logger.warning(f"JSON parsing failed for {api_name}, attempting text parsing")
            prediction_data = self._parse_text_response(response_content)
            
            return self._format_prediction_result(
                prediction_data, api_name, model_name, response_content
            )
            
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return self._create_error_result(response_content, api_name, model_name, str(e))
    
    def _parse_json_response(self, response_content: str) -> Optional[Dict[str, Any]]:
        """
        Parse JSON response from LLM.
        
        Args:
            response_content: Raw response content
            
        Returns:
            Parsed JSON data or None if parsing fails
        """
        try:
            # Try to find JSON in the response
            json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                return json.loads(json_str)
            
            # Try parsing the entire response as JSON
            return json.loads(response_content)
            
        except (json.JSONDecodeError, AttributeError) as e:
            logger.debug(f"JSON parsing failed: {e}")
            return None
    
    def _parse_text_response(self, response_content: str) -> Dict[str, Any]:
        """
        Parse text response using regex patterns.
        
        Args:
            response_content: Raw response content
            
        Returns:
            Extracted prediction data
        """
        prediction_data = {
            "short_term": {},
            "medium_term": {},
            "long_term": {},
            "dominant_theme": "",
            "critical_levels": {}
        }
        
        # Extract short-term outlook
        short_term = self._extract_outlook_section(response_content, "SHORT-TERM")
        if short_term:
            prediction_data["short_term"] = short_term
        
        # Extract medium-term outlook
        medium_term = self._extract_outlook_section(response_content, "MEDIUM-TERM")
        if medium_term:
            prediction_data["medium_term"] = medium_term
        
        # Extract long-term outlook
        long_term = self._extract_outlook_section(response_content, "LONG-TERM")
        if long_term:
            prediction_data["long_term"] = long_term
        
        # Extract dominant theme
        theme_match = re.search(r"DOMINANT MARKET THEME[:\s]*(.+?)(?:\n\n|\n[A-Z]|$)", 
                               response_content, re.DOTALL | re.IGNORECASE)
        if theme_match:
            prediction_data["dominant_theme"] = theme_match.group(1).strip()
        
        # Extract critical levels
        levels = self._extract_critical_levels(response_content)
        if levels:
            prediction_data["critical_levels"] = levels
        
        return prediction_data
    
    def _extract_outlook_section(self, text: str, term: str) -> Dict[str, Any]:
        """Extract outlook section (short/medium/long-term) from text."""
        pattern = rf"{term}.*?OUTLOOK.*?\n(.*?)(?:\n\n|\n[A-Z]|$)"
        match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
        
        if not match:
            return {}
        
        section_text = match.group(1)
        
        # Extract direction
        direction_match = re.search(r"Direction:\s*\[?(UP|DOWN|FLAT)\]?", 
                                   section_text, re.IGNORECASE)
        direction = direction_match.group(1).upper() if direction_match else "FLAT"
        
        # Extract expected move
        move_match = re.search(r"Expected Move:\s*\[?([^\]]+)\]?", 
                              section_text, re.IGNORECASE)
        expected_move = move_match.group(1).strip() if move_match else "0-1%"
        
        # Extract confidence
        conf_match = re.search(r"Confidence:\s*\[?(HIGH|MEDIUM|LOW)\]?", 
                              section_text, re.IGNORECASE)
        confidence = conf_match.group(1).upper() if conf_match else "LOW"
        
        # Extract key evidence
        evidence_pattern = r"KEY EVIDENCE:\s*(.*?)(?:\n\n|\n[A-Z]|$)"
        evidence_match = re.search(evidence_pattern, section_text, re.DOTALL | re.IGNORECASE)
        
        key_evidence = []
        if evidence_match:
            evidence_text = evidence_match.group(1).strip()
            # Split by bullet points or numbered lists
            evidence_items = re.split(r'[-•\d+\.]\s*', evidence_text)
            key_evidence = [item.strip() for item in evidence_items if item.strip()][:3]
        
        return {
            "direction": direction,
            "expected_move_pct": expected_move,
            "confidence": confidence,
            "key_evidence": key_evidence
        }
    
    def _extract_critical_levels(self, text: str) -> Dict[str, Any]:
        """Extract critical levels from text."""
        levels = {}
        
        # Extract support level
        support_match = re.search(r"Support:\s*\[?([^\]]+)\]?", text, re.IGNORECASE)
        if support_match:
            try:
                levels["support"] = float(re.search(r'[\d.]+', support_match.group(1)).group())
            except (AttributeError, ValueError):
                pass
        
        # Extract resistance level
        resistance_match = re.search(r"Resistance:\s*\[?([^\]]+)\]?", text, re.IGNORECASE)
        if resistance_match:
            try:
                levels["resistance"] = float(re.search(r'[\d.]+', resistance_match.group(1)).group())
            except (AttributeError, ValueError):
                pass
        
        # Extract trigger
        trigger_match = re.search(r"Breakout/Breakdown trigger:\s*\[?([^\]]+)\]?", 
                                 text, re.IGNORECASE)
        if trigger_match:
            levels["trigger"] = trigger_match.group(1).strip()
        
        return levels
    
    def _format_prediction_result(
        self, 
        prediction_data: Dict[str, Any],
        api_name: str,
        model_name: str,
        raw_response: str
    ) -> Dict[str, Any]:
        """Format prediction data into standardized result."""
        
        # Extract primary prediction (use short-term as primary)
        primary_prediction = prediction_data.get("short_term", {})
        direction = primary_prediction.get("direction", "FLAT")
        confidence_level = primary_prediction.get("confidence", "LOW")
        
        # Convert confidence to numeric score
        confidence_score = self._confidence_to_score(confidence_level)
        
        # Extract key evidence
        key_evidence = primary_prediction.get("key_evidence", [])
        
        # Create probability scores based on direction and confidence
        probabilities = self._create_probability_scores(direction, confidence_score)
        
        return {
            "prediction": direction.lower(),
            "confidence": confidence_score,
            "probabilities": probabilities,
            "key_evidence": key_evidence,
            "detailed_outlook": {
                "short_term": prediction_data.get("short_term", {}),
                "medium_term": prediction_data.get("medium_term", {}),
                "long_term": prediction_data.get("long_term", {})
            },
            "dominant_theme": prediction_data.get("dominant_theme", ""),
            "critical_levels": prediction_data.get("critical_levels", {}),
            "metadata": {
                "api": api_name,
                "model": model_name,
                "timestamp": datetime.now().isoformat(),
                "raw_response": raw_response
            }
        }
    
    def _confidence_to_score(self, confidence_level: str) -> float:
        """Convert confidence level to numeric score."""
        confidence_map = {
            "HIGH": 0.85,
            "MEDIUM": 0.65,
            "LOW": 0.45
        }
        return confidence_map.get(confidence_level.upper(), 0.45)
    
    def _create_probability_scores(self, direction: str, confidence: float) -> Dict[str, float]:
        """Create probability scores based on direction and confidence."""
        base_prob = (1.0 - confidence) / 2  # Split remaining probability
        
        if direction.upper() == "UP":
            return {
                "positive": confidence,
                "negative": base_prob,
                "neutral": base_prob
            }
        elif direction.upper() == "DOWN":
            return {
                "positive": base_prob,
                "negative": confidence,
                "neutral": base_prob
            }
        else:  # FLAT
            return {
                "positive": base_prob,
                "negative": base_prob,
                "neutral": confidence
            }
    
    def _create_error_result(
        self, 
        raw_response: str,
        api_name: str,
        model_name: str,
        error_message: str
    ) -> Dict[str, Any]:
        """Create error result when parsing fails."""
        return {
            "prediction": "neutral",
            "confidence": 0.33,
            "probabilities": {
                "positive": 0.33,
                "negative": 0.33,
                "neutral": 0.34
            },
            "key_evidence": ["Unable to parse LLM response"],
            "detailed_outlook": {},
            "dominant_theme": "Analysis unavailable due to parsing error",
            "critical_levels": {},
            "metadata": {
                "api": api_name,
                "model": model_name,
                "timestamp": datetime.now().isoformat(),
                "raw_response": raw_response,
                "error": error_message
            }
        }
